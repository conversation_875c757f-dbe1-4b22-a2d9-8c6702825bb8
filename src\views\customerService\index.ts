/*
 * @description:
 * @Author: lexy
 * @Date: 2022-11-01 16:17:13
 * @LastEditors: lexy
 * @LastEditTime: 2023-08-10 11:04:11
 */
import { ref, computed, reactive, nextTick } from 'vue'
import { ChatMessage, IPage, MessageAndShopAdmin, MessageUser, CustomerServiceMessage } from '@/views/customerService/types'
import { stompHookMount } from '@/libs/stomp/StompHandler'
import { Channel } from '@/libs/stomp/typs'
import { getMessages, getMessageUsers, sendMessages, getPlatformChatRooms } from '@/apis/message'
import type { Page } from '@/apis/message'
import { useAdminInfo } from '@/store/modules/admin'
import { useRoute, useRouter } from 'vue-router'
import { MessageType } from '@/apis/message'

export const shopInfo = ref(useAdminInfo().getterAdminInfo)
useAdminInfo().$subscribe((mutation, state) => {
    const info = state.adminInfo
    shopInfo.value = info
    console.log('shopInfo 更新:', info)
    if (!info || !info.value) {
        console.log('shopInfo 无效，跳过加载')
        return
    }
    currentSelectUser.value = undefined
    messageUsersPage.initLoad()
})
/***** constant ******/

const searchKeyword = ref('')
/***** user list ******/
export const messageUsersPage = reactive<IPage<MessageUser>>(
    new IPage<MessageUser>(
        (page: Page) => {
            const params = {
                ...page,
                chatWithType: 'PLAT_FORM',
                shopId: shopInfo.value?.additionalInformation?.shopId || ''
            }
            console.log('调用 getMessageUsers，参数:', {
                keyword: searchKeyword.value,
                params
            })
            return getMessageUsers(searchKeyword.value, params).then((response: any) => {
                console.log('getMessageUsers 接口响应:', response)
                console.log('响应数据结构:', JSON.stringify(response, null, 2))

                // 转换新接口的数据结构为前端期望的格式
                if (response && response.data && response.data.records) {
                    response.data.records = response.data.records.map((item: any) => {
                        // 如果是新接口格式（有chatWithShopInfo），转换为旧格式（chatWithUserInfo）
                        if (item.chatWithShopInfo && !item.chatWithUserInfo) {
                            return {
                                ...item,
                                chatWithUserInfo: {
                                    avatar: item.lastMessage?.sender?.senderUserInfo?.avatar || '',
                                    nickname: item.chatWithShopInfo.shopName || item.lastMessage?.sender?.senderUserInfo?.nickname || '',
                                    userId: item.chatWithShopInfo.shopId || '',
                                    userKey: item.chatWithShopInfo.shopId || '',
                                    includeRights: false,
                                },
                            }
                        }
                        return item
                    })
                    console.log('转换后的数据:', response.data.records)
                    console.log('第一个用户的详细信息:', JSON.stringify(response.data.records[0], null, 2))
                }

                return response
            }).catch((error: any) => {
                console.error('getMessageUsers 接口错误:', error)
                throw error
            })
        },
        30,
    ),
)
export const currentSelectUser = ref<MessageUser>()
export const searchFocus = ref(false)
export const onChange = (selectUser: MessageUser) => {
    currentSelectUser.value = selectUser
    nextTick(() => {
        if (selectUser.chatWithUserInfo.userId) {
            adminMessagesPage.value.initLoad()
        }
    })
}
export const onKeywordsChange = (keyword: string) => {
    if (keyword !== searchKeyword.value) {
        searchKeyword.value = keyword
        messageUsersPage.initLoad()
    }
}
export const onSearchFocus = (val: boolean) => {
    searchFocus.value = val
}

const shopId = computed(() => (shopId?: string) => {
    if (shopId) {
        return shopId
    }
    // 使用shopId而不是userId
    return currentSelectUser.value?.chatWithUserInfo.userId || ''
})

/***** user messages ******/
/**
 * 加载用户消息
 */
export const adminMessagesPage = ref<IPage<MessageAndShopAdmin>>(
    new IPage<MessageAndShopAdmin>((page: Page) => {
        const shopIdValue = shopId.value() ? shopId.value() : '-1'
        console.log('调用 getMessages，参数:', { senderShopId: shopIdValue, page })
        return getMessages(shopIdValue, page).then((response: any) => {
            console.log('getMessages 接口响应:', response)
            console.log('消息数据:', JSON.stringify(response, null, 2))
            return response
        }).catch((error: any) => {
            console.error('getMessages 接口错误:', error)
            throw error
        })
    }, 20),
)
export const messageSubmit = async ({ message, messageType }: ChatMessage) => {
    if (!currentSelectUser.value) return

    const data = {
        receiverId: shopId.value(),
        senderId: shopInfo.value.additionalInformation.shopId,
        messageType,
        content: message,
    }

    try {
        console.log('发送消息:', data)

        // 先创建本地消息显示
        const localMessage: MessageAndShopAdmin = {
            handled: false,
            message: message,
            messageType: messageType,
            read: true,
            receiver: {
                receiverShopInfo: { shopId: '', shopName: '' },
                receiverType: 'USER',
            },
            sendTime: Date.now().toString(),
            sender: {
                senderType: 'PLATFORM_ADMIN',
                senderUserInfo: {
                    avatar: '',
                    nickname: shopInfo.value.nickname || '客服',
                    userId: shopInfo.value.additionalInformation.userId,
                    userKey: shopInfo.value.additionalInformation.userId,
                    includeRights: false,
                },
                senderShopInfo: {
                    shopId: shopInfo.value.additionalInformation.shopId,
                    shopName: '',
                },
            },
            show: true,
        }

        // 立即添加到消息列表
        adminMessagesPage.value.concatData(localMessage)

        // 发送消息到服务器
        await sendMessages(data)
        console.log('消息发送成功')

        // 更新用户列表中的最后一条消息
        if (currentSelectUser.value) {
            currentSelectUser.value.lastMessage.message = message
            currentSelectUser.value.lastMessage.messageType = messageType
            currentSelectUser.value.lastMessage.sendTime = Date.now().toString()
            currentSelectUser.value.lastMessage.show = false
        }

    } catch (error) {
        console.error('发送消息失败:', error)
        // 发送失败时，可以选择移除刚添加的本地消息
        // adminMessagesPage.value.records = adminMessagesPage.value.records.filter(msg => msg !== localMessage)
    }
}
export const pageInit = () => {
    messageUsersPage.initLoad()
}

/**
 * 加载初始数据
 */
const loadInitialData = async () => {
    try {
        console.log('开始加载初始数据...')
        console.log('当前shopInfo:', shopInfo.value)

        if (!shopInfo.value?.additionalInformation?.shopId) {
            console.log('shopInfo未准备好，跳过数据加载')
            return
        }

        console.log('开始加载用户列表...')
        await messageUsersPage.initLoad()
        console.log('用户列表加载完成，用户数量:', messageUsersPage.records.length)

        // 额外调用平台聊天室分页接口
        console.log('开始调用平台聊天室分页接口...')
        await loadPlatformChatRooms()
        console.log('平台聊天室分页接口调用完成')

    } catch (error) {
        console.error('加载初始数据失败:', error)
    }
}

// 调用平台聊天室分页接口（不需要传递ID）
const loadPlatformChatRooms = async () => {
    try {
        const pageParams = {
            current: 1,
            size: 30
        }

        console.log('调用 getPlatformChatRooms，参数:', pageParams)
        const response = await getPlatformChatRooms(pageParams)
        console.log('getPlatformChatRooms 接口响应:', response)
        console.log('平台聊天室数据:', JSON.stringify(response, null, 2))

        // 这里可以根据需要处理返回的数据
        // 例如：合并到现有用户列表或者单独处理

    } catch (error) {
        console.error('调用平台聊天室接口失败:', error)
    }
}

/**
 * 初始化 用户列表 并且 监听  客服消息
 */
export const initCustomerService = async () => {
    //初始化用户列表
    // await messageUsersPage.initLoad().then((res) => {
    //     adminMessagesPage.value = new IPage<MessageAndShopAdmin>(
    //         (page: Page) => getMessages(userId.value() ? userId.value() : res[0].userId, page),
    //         20,
    //     )
    // })
    console.log('=== 开始初始化客服服务 ===')
    console.log('当前时间:', new Date().toLocaleString())

    // 立即尝试加载数据
    await loadInitialData()

    // 如果shopInfo还没准备好，监听其变化
    if (!shopInfo.value?.additionalInformation?.shopId) {
        console.log('shopInfo 未准备好，开始监听变化...')
        const unsubscribe = useAdminInfo().$subscribe(async (_mutation, state) => {
            const info = state.adminInfo
            console.log('shopInfo 状态变化:', info)
            if (info?.additionalInformation?.shopId) {
                await loadInitialData()
                unsubscribe() // 取消订阅，避免重复加载
            }
        })
    } else {
        console.log('shopInfo 已准备好:', shopInfo.value)
    }

    // 检查是否需要创建新供应商对话并发送消息
    const route = useRoute()
    const router = useRouter()
    console.log('=== 检查路由参数 ===')
    console.log('完整路由对象:', route)
    console.log('路由参数 route.query:', route.query)
    console.log('路由参数类型:', typeof route.query)
    console.log('参数详情:')
    console.log('  - newSupplierUserId:', route.query.newSupplierUserId)
    console.log('  - supplierName:', route.query.supplierName)
    console.log('  - autoSendMessage:', route.query.autoSendMessage)
    console.log('  - orderNo:', route.query.orderNo)
    console.log('  - shopId:', route.query.shopId)

    if (route.query.newSupplierUserId && route.query.autoSendMessage) {
        console.log('检测到催发货参数，准备创建供应商对话:', {
            supplierUserId: route.query.newSupplierUserId,
            supplierName: route.query.supplierName,
            message: route.query.autoSendMessage,
            orderNo: route.query.orderNo
        })

        // 立即处理，不延迟，确保用户体验
        await nextTick() // 确保DOM更新完成

        // 显示正在建立对话的提示
        console.log(`正在与供应商 ${route.query.supplierName} 建立对话...`)

        handleCreateSupplierAndSendMessage(
            route.query.newSupplierUserId as string,
            route.query.supplierName as string,
            route.query.autoSendMessage as string
        )

        // 清除URL参数，避免刷新页面时重复执行
        const newQuery = { ...route.query }
        delete newQuery.newSupplierUserId
        delete newQuery.supplierName
        delete newQuery.autoSendMessage
        delete newQuery.orderNo

        // 使用replace避免在浏览器历史中留下带参数的记录
        router.replace({
            name: 'customerService',
            query: Object.keys(newQuery).length > 0 ? newQuery : undefined
        })
    } else if (route.query.shopId) {
        console.log('检测到shopId参数，准备定位到对应的对话:', route.query.shopId)

        // 等待用户列表加载完成
        await nextTick()

        // 查找对应shopId的用户
        await handleLocateShopConversation(route.query.shopId as string)

        // 清除URL参数，避免刷新页面时重复执行
        const newQuery = { ...route.query }
        delete newQuery.shopId

        // 使用replace避免在浏览器历史中留下带参数的记录
        router.replace({
            name: 'customerService',
            query: Object.keys(newQuery).length > 0 ? newQuery : undefined
        })
    } else {
        console.log('未检测到特殊参数')
    }

    //监听用户和店铺消息
    stompHookMount(Channel.PLATFORM_SHOP_AND_USER, {
        success: pageInit,
        fail: (error) => {
            console.error('WebSocket connection failed:', error)
        },
        subscribe: (message) => {
            console.log('Received message:', message)
            messageUsersOnNewMessage(message as unknown as MessageAndShopAdmin)
        },
    })
}
/**
 * 新的消息处理
 * @param message
 */
const messageUsersOnNewMessage = (message: MessageAndShopAdmin) => {
    messageUsersPage.initLoad()
    const messageShopId = message.sender.senderUserInfo?.userId || message.sender.senderShopInfo?.shopId

    if (messageShopId === currentSelectUser.value?.chatWithUserInfo.userId || messageShopId === shopInfo.value.additionalInformation.shopId) {
        adminMessagesPage.value.concatData(message)
    }
}

export const contentLoadMore = () => {
    adminMessagesPage.value.loadMore()
}

// 真正建立供应商会话关系并发送消息
const handleCreateSupplierAndSendMessage = async (
    supplierUserId: string,
    supplierName: string,
    message: string
) => {
    console.log('=== 开始建立真实会话关系 ===')
    console.log('供应商信息:', { supplierUserId, supplierName, message })

    try {
        // 检查 shopInfo 是否已加载
        if (!shopInfo.value?.additionalInformation?.shopId) {
            console.error('shopInfo 未加载完成，等待重试...')
            setTimeout(() => {
                handleCreateSupplierAndSendMessage(supplierUserId, supplierName, message)
            }, 500)
            return
        }

        console.log('shopInfo 已加载:', shopInfo.value)

        // 1. 首先发送消息给供应商，这会在后端自动创建会话关系
        const messageData = {
            receiverId: supplierUserId,
            senderId: shopInfo.value.additionalInformation.shopId,
            messageType: MessageType.TEXT,
            content: message
        }

        console.log('=== 第一步：发送消息建立会话关系 ===')
        console.log('消息数据:', messageData)

        // 发送消息，这会在后端创建真实的会话关系
        const sendResult = await sendMessages(messageData)
        console.log('消息发送结果:', sendResult)

        if (sendResult.code === 200 || sendResult.code === 0) {
            console.log('✅ 消息发送成功，会话关系已建立')

            // 2. 刷新用户列表，获取真实的会话数据
            console.log('=== 第二步：刷新用户列表获取真实会话 ===')
            await messageUsersPage.initLoad()
            console.log('用户列表刷新完成，当前用户数量:', messageUsersPage.records.length)

            // 3. 查找刚创建的供应商会话
            const supplierUser = messageUsersPage.records.find(
                user => user.chatWithUserInfo.userId === supplierUserId
            )

            if (supplierUser) {
                console.log('✅ 找到供应商会话:', supplierUser)
                // 选中该供应商
                currentSelectUser.value = supplierUser

                // 4. 加载该供应商的消息历史
                console.log('=== 第三步：加载消息历史 ===')
                await nextTick()
                if (supplierUser.chatWithUserInfo.userId) {
                    await adminMessagesPage.value.initLoad()
                    console.log('✅ 消息历史加载完成')
                }
            } else {
                console.warn('⚠️ 未找到供应商会话，创建临时显示')
                // 如果没找到，创建临时显示（备用方案）
                createTemporarySupplierDisplay(supplierUserId, supplierName, message)
            }

        } else {
            console.error('❌ 消息发送失败:', sendResult)
            throw new Error(`消息发送失败: ${sendResult.msg || '未知错误'}`)
        }

        console.log('=== 会话建立完成 ===')

    } catch (error) {
        console.error('❌ 建立会话关系失败:', error)

        // 发生错误时，创建临时显示让用户可以手动重试
        console.log('创建临时显示作为备用方案')
        createTemporarySupplierDisplay(supplierUserId, supplierName, message)
    }
}

// 创建临时供应商显示（备用方案）
const createTemporarySupplierDisplay = (
    supplierUserId: string,
    supplierName: string,
    message: string
) => {
    console.log('创建临时供应商显示:', { supplierUserId, supplierName })

    const tempSupplierUser: MessageUser = {
        chatWithUserInfo: {
            avatar: '',
            nickname: `${supplierName} (临时)`,
            userId: supplierUserId,
            userKey: supplierUserId,
            includeRights: false
        },
        lastMessage: {
            handled: false,
            message: message,
            messageType: MessageType.TEXT,
            read: true,
            receiver: {
                receiverShopInfo: { shopId: '', shopName: '' },
                receiverType: 'USER'
            },
            sendTime: Date.now().toString(),
            sender: {
                senderType: 'PLATFORM_ADMIN',
                senderUserInfo: {
                    avatar: '',
                    nickname: shopInfo.value.nickname || '客服',
                    userId: shopInfo.value.additionalInformation.userId,
                    userKey: shopInfo.value.additionalInformation.userId,
                    includeRights: false,
                },
                senderShopInfo: {
                    shopId: shopInfo.value.additionalInformation.shopId,
                    shopName: '',
                },
            },
            show: false,
        }
    }

    // 添加到用户列表
    messageUsersPage.records.unshift(tempSupplierUser)
    currentSelectUser.value = tempSupplierUser

    // 创建本地消息显示
    const localMessage: MessageAndShopAdmin = {
        handled: false,
        message: `${message}\n\n⚠️ 这是临时显示，请手动重新发送消息建立真实会话`,
        messageType: MessageType.TEXT,
        read: true,
        receiver: {
            receiverShopInfo: { shopId: '', shopName: '' },
            receiverType: 'USER',
        },
        sendTime: Date.now().toString(),
        sender: {
            senderType: 'PLATFORM_ADMIN',
            senderUserInfo: {
                avatar: '',
                nickname: shopInfo.value.nickname || '客服',
                userId: shopInfo.value.additionalInformation.userId,
                userKey: shopInfo.value.additionalInformation.userId,
                includeRights: false,
            },
            senderShopInfo: {
                shopId: shopInfo.value.additionalInformation.shopId,
                shopName: '',
            },
        },
        show: true,
    }

    adminMessagesPage.value.records = [localMessage]
    console.log('临时显示创建完成')
}

// 定位到指定shopId的对话
const handleLocateShopConversation = async (shopId: string) => {
    console.log('=== 开始定位shopId对话 ===')
    console.log('目标shopId:', shopId)

    try {
        // 确保用户列表已加载
        if (messageUsersPage.records.length === 0) {
            console.log('用户列表为空，重新加载...')
            await messageUsersPage.initLoad()
        }

        console.log('当前用户列表数量:', messageUsersPage.records.length)

        // 查找对应shopId的用户（在chatWithUserInfo.userId中查找）
        const targetUser = messageUsersPage.records.find((user) => {
            const userId = user.chatWithUserInfo?.userId
            console.log('检查用户:', { userId, targetShopId: shopId })
            return userId === shopId
        })

        if (targetUser) {
            console.log('✅ 找到目标用户:', targetUser)
            // 选中该用户
            currentSelectUser.value = targetUser

            // 加载该用户的消息历史
            await nextTick()
            if (targetUser.chatWithUserInfo?.userId) {
                await adminMessagesPage.value.initLoad()
                console.log('✅ 消息历史加载完成')
            }
        } else {
            console.warn('⚠️ 未找到对应shopId的用户:', shopId)
            console.log(
                '当前用户列表:',
                messageUsersPage.records.map((user) => ({
                    userId: user.chatWithUserInfo?.userId,
                    nickname: user.chatWithUserInfo?.nickname,
                })),
            )
        }

        console.log('=== shopId对话定位完成 ===')

    } catch (error) {
        console.error('❌ 定位shopId对话失败:', error)
    }
}
